import streamlit as st
import pandas as pd
import numpy as np
import tensorflow as tf
import matplotlib.pyplot as plt
import seaborn as sns
from tensorflow.keras.models import Sequential, load_model
from tensorflow.keras.layers import LSTM, Dense, Dropout
from sklearn.preprocessing import MinMaxScaler
import joblib
import os

def load_processed_data(file):
    df = pd.read_csv(file)
    df['DATE'] = pd.to_datetime(df['DATE'])
    df = df.sort_values(by='DATE')
    return df

def prepare_features(df):
    feature_cols = ['USD_PRICE', 'EUROPE_EUR_', 'JAPAN_JPY_', 'UNITED_KINGDOM_GBP_', 'CANADA_CAD_', 
                    'SWITZERLAND_CHF_', 'INDIA_INR_', 'CHINA_CNY_', 'TURKEY_TRY_', 'SAUDI_ARABIA_SAR_',
                    'INDONESIA_IDR_', 'UNITED_ARAB_EMIRATES_AED_', 'THAILAND_THB_', 'VIETNAM_VND_',
                    'EGYPT_EGP_', 'SOUTH_KOREAN_KRW_', 'AUSTRALIA_AUD_', 'SOUTH_AFRICA_ZAR_',
                    'MA_7_USD_PRICE', 'MA_30_USD_PRICE', 'Volatility_30_USD_PRICE', 'Momentum_7_USD_PRICE']
    df = df.dropna(subset=feature_cols)
    scaler = MinMaxScaler()
    scaled_data = scaler.fit_transform(df[feature_cols])
    if not os.path.exists('models'):
        os.makedirs('models')
    joblib.dump(scaler, 'models/scaler.pkl')
    return scaled_data, df['DATE'].values, df

def create_sequences(data, seq_length=30):
    X, y = [], []
    for i in range(len(data) - seq_length):
        X.append(data[i:i+seq_length])
        y.append(data[i+seq_length, 0])
    return np.array(X), np.array(y)

def build_lstm_model(input_shape):
    model = Sequential([
        LSTM(50, return_sequences=True, input_shape=input_shape),
        Dropout(0.2),
        LSTM(50, return_sequences=False),
        Dropout(0.2),
        Dense(25),
        Dense(1)
    ])
    model.compile(optimizer='adam', loss='mean_squared_error')
    return model

def train_model(df, seq_length=30, epochs=500, batch_size=20):
    scaled_data, dates, _ = prepare_features(df)
    X, y = create_sequences(scaled_data, seq_length)
    X = np.reshape(X, (X.shape[0], X.shape[1], X.shape[2]))
    
    model = build_lstm_model((seq_length, X.shape[2]))
    model.fit(X, y, epochs=epochs, batch_size=batch_size, validation_split=0.1)
    
    model.save('models/gold_price_lstm.h5')
    return "Model training complete!"

def plot_gold_prices(df):
    st.subheader("Historical Gold Prices")
    plt.figure(figsize=(12,6))
    sns.lineplot(x=df['DATE'], y=df['USD_PRICE'], label='Gold Price in USD')
    plt.xlabel("Date")
    plt.ylabel("Gold Price (USD)")
    plt.title("Gold Price Trend Over Time")
    st.pyplot(plt)

def plot_moving_averages(df):
    st.subheader("Moving Averages & Volatility")
    plt.figure(figsize=(12,6))
    plt.plot(df['DATE'], df['USD_PRICE'], label='Gold Price', alpha=0.5)
    plt.plot(df['DATE'], df['MA_7_USD_PRICE'], label='7-Day MA', linestyle='dashed')
    plt.plot(df['DATE'], df['MA_30_USD_PRICE'], label='30-Day MA', linestyle='dashed')
    plt.fill_between(df['DATE'], df['USD_PRICE'] - df['Volatility_30_USD_PRICE'],
                     df['USD_PRICE'] + df['Volatility_30_USD_PRICE'], alpha=0.2, label='30-Day Volatility')
    plt.legend()
    plt.xlabel("Date")
    plt.ylabel("Price (USD)")
    plt.title("Gold Price Moving Averages and Volatility")
    st.pyplot(plt)

def predict_future_price(df, seq_length=30):
    try:
        model = load_model('models/gold_price_lstm.h5')
        scaler = joblib.load('models/scaler.pkl')
        scaled_data, _, _ = prepare_features(df)
        last_sequence = scaled_data[-seq_length:]
        last_sequence = np.reshape(last_sequence, (1, seq_length, last_sequence.shape[1]))
        predicted_price = model.predict(last_sequence)
        predicted_price = scaler.inverse_transform(np.pad(predicted_price, ((0,0),(0,scaled_data.shape[1]-1)), 'constant'))[:,0]
        return predicted_price[0]
    except Exception as e:
        return f"Error in prediction: {e}"

# Streamlit UI
st.title("Gold Price Prediction Model")

uploaded_file = st.file_uploader("Upload your preprocessed CSV file", type=["csv"])

if uploaded_file is not None:
    st.write("Processing the uploaded data...")
    df = load_processed_data(uploaded_file)
    st.write("First few rows of the dataset:")
    st.dataframe(df.head())
    
    plot_gold_prices(df)
    plot_moving_averages(df)
    
    if st.button("Train Model"):
        result = train_model(df)
        st.success(result)
    
    if st.button("Predict Future Price"):
        predicted_price = predict_future_price(df)
        st.write(f"Predicted Gold Price for Next Day: ${predicted_price:.2f}")
