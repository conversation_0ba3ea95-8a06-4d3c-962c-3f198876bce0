# Gold Price Analyzer

A web application to analyze gold prices, visualize import data, and fetch the latest gold-related news.

## Features
- Gold price prediction using machine learning.
- Interactive visualization of gold import data.
- Latest gold-related news from an API.

## Installation
1. Clone the repository.
2. Install dependencies: `pip install -r requirements.txt`
3. Run the Flask app: `python app.py`
4. Open `http://localhost:5000` in your browser.