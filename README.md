# Gold Price Analyzer

A comprehensive web application to analyze gold prices, predict future trends using machine learning, visualize import data, and fetch the latest gold-related news.

## Features
- 🔮 **Gold price prediction** using LSTM and Random Forest machine learning models
- 📊 **Interactive data visualization** of gold import data with Plotly
- 📰 **Latest gold-related news** from NewsData.io API
- 📈 **Market analysis** with price trends and moving averages
- 🌐 **Modern web interface** built with Flask and responsive design

## Technologies Used
- **Backend**: Python, Flask
- **Machine Learning**: TensorFlow/Keras (LSTM), Scikit-learn (Random Forest)
- **Data Processing**: Pandas, NumPy
- **Visualization**: Plotly
- **Frontend**: HTML5, CSS3, JavaScript
- **API Integration**: NewsData.io for news feeds

## Installation & Setup

### Prerequisites
- Python 3.8 or higher
- pip (Python package installer)

### Step 1: Clone the Repository
```bash
git clone https://github.com/yourusername/My-Projects.git
cd My-Projects
```

### Step 2: Create Virtual Environment (Recommended)
```bash
python -m venv venv
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate
```

### Step 3: Install Dependencies
```bash
pip install -r requirements.txt
```

### Step 4: Environment Configuration
1. Copy the example environment file:
   ```bash
   cp .env.example .env
   ```

2. Edit `.env` file and add your API keys:
   ```
   NEWSDATA_API_KEY=your_actual_api_key_here
   ```

3. Get your free NewsData.io API key from: https://newsdata.io/

### Step 5: Run the Application
```bash
python app.py
```

The application will be available at `http://localhost:5000`

## Project Structure
```
Gold_price_analyzer/
├── app.py                 # Main Flask application
├── ml_model.py           # Machine learning models
├── data_preprocessor.py  # Data processing utilities
├── requirements.txt      # Python dependencies
├── .env.example         # Environment variables template
├── data/                # Data files
│   ├── processed/       # Processed data files
│   └── *.csv           # Raw data files
├── models/              # Trained ML models
├── static/              # Static assets (CSS, JS)
├── templates/           # HTML templates
└── README.md           # This file
```

## Usage

### Home Page
- Overview of the application features
- Navigation to different sections

### Market Analysis
- View latest gold prices
- Weekly price averages
- Latest gold-related news

### Price Prediction
- Select country for prediction
- Generate future price forecasts using ML models
- View prediction results

## API Keys & Security
- All sensitive information is stored in environment variables
- Never commit `.env` file to version control
- Use `.env.example` as a template for required variables

## Contributing
1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License
This project is licensed under the MIT License - see the LICENSE file for details.

## Contact
- Email: <EMAIL>
- Project Link: https://github.com/yourusername/My-Projects