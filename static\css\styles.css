/* General Styles */
body {
    font-family: 'Segoe UI', sans-serif;
    line-height: 1.6;
    color: #333;
    margin: 0;
    padding: 0;
}

/* Header Styles */
header {
    background: #2c3e50;
    color: white;
    padding: 1rem 2rem;
}

nav a {
    color: white;
    text-decoration: none;
    margin-right: 1.5rem;
}

/* Hero Section */
.hero {
    background: #3498db;
    color: white;
    padding: 4rem 2rem;
    text-align: center;
}

.hero h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.cta-button {
    background: #e67e22;
    color: white;
    padding: 0.8rem 2rem;
    border-radius: 5px;
    text-decoration: none;
}

/* Services Section */
.services-overview {
    padding: 2rem;
}

.service-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.card {
    background: white;
    padding: 1.5rem;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

/* Footer Styles */
footer {
    background: #34495e;
    color: white;
    padding: 2rem;
    text-align: center;
}

footer a {
    color: #3498db;
    text-decoration: none;
}

nav {
    background-color: #333;
    padding: 10px;
}

nav ul {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
    justify-content: space-around;
}

nav ul li {
    display: inline;
}

nav ul li a {
    color: white;
    text-decoration: none;
}

h1 {
    text-align: center;
    margin-top: 20px;
}

form {
    text-align: center;
    margin-top: 20px;
}

#chart {
    width: 80%;
    margin: 0 auto;
}