import pandas as pd
from pathlib import Path

def clean_date(date_str):
    """Robust date parser with multiple format support"""
    formats = [
        '%d-%m-%Y', '%m/%d/%Y', '%Y.%m.%d',
        '%d-%b-%Y', '%b %d, %Y', '%Y%m%d'
    ]
    
    for fmt in formats:
        try:
            return pd.to_datetime(date_str, format=fmt, exact=True)
        except:
            continue
    return pd.NaT

def preprocess_gold_data(input_path, output_dir='data/processed'):
    # Create output directory
    Path(output_dir).mkdir(parents=True, exist_ok=True)
    
    # Load raw data
    raw_df = pd.read_csv(input_path)
    
    # Standardize column names
    raw_df.columns = (
        raw_df.columns
        .str.strip()
        .str.upper()
        .str.replace('[^A-Z0-9]', '_', regex=True)
    )
    
    # Map raw column names to standardized names
    column_mapping = {
        'DATE': 'DATE',
        'UNITED_STATES_USD': 'USD_PRICE',
        'UNITED_STATES_USD_': 'USD_PRICE',
        'UNITED_STATES_(USD)': 'USD_PRICE',
        'EUROPE_EUR': 'EUR_PRICE',
        'EUROPE_(EUR)': 'EUR_PRICE',
        'JAPAN_JPY': 'JPY_PRICE',
        'JAPAN_(JPY)': 'JPY_PRICE',
        'UNITED_KINGDOM_GBP': 'GBP_PRICE',
        'UNITED_KINGDOM_(GBP)': 'GBP_PRICE',
        'CANADA_CAD': 'CAD_PRICE',
        'CANADA_(CAD)': 'CAD_PRICE',
        'SWITZERLAND_CHF': 'CHF_PRICE',
        'SWITZERLAND_(CHF)': 'CHF_PRICE',
        'INDIA_INR': 'INR_PRICE',
        'INDIA_(INR)': 'INR_PRICE',
        'CHINA_CNY': 'CNY_PRICE',
        'CHINA_(CNY)': 'CNY_PRICE',
        'TURKEY_TRY': 'TRY_PRICE',
        'TURKEY_(TRY)': 'TRY_PRICE',
        'SAUDI_ARABIA_SAR': 'SAR_PRICE',
        'SAUDI_ARABIA_(SAR)': 'SAR_PRICE',
        'INDONESIA_IDR': 'IDR_PRICE',
        'INDONESIA_(IDR)': 'IDR_PRICE',
        'UNITED_ARAB_EMIRATES_AED': 'AED_PRICE',
        'UNITED_ARAB_EMIRATES_(AED)': 'AED_PRICE',
        'THAILAND_THB': 'THB_PRICE',
        'THAILAND_(THB)': 'THB_PRICE',
        'VIETNAM_VND': 'VND_PRICE',
        'VIETNAM_(VND)': 'VND_PRICE',
        'EGYPT_EGP': 'EGP_PRICE',
        'EGYPT_(EGP)': 'EGP_PRICE',
        'SOUTH_KOREAN_KRW': 'KRW_PRICE',
        'SOUTH_KOREAN_(KRW)': 'KRW_PRICE',
        'AUSTRALIA_AUD': 'AUD_PRICE',
        'AUSTRALIA_(AUD)': 'AUD_PRICE',
        'SOUTH_AFRICA_ZAR': 'ZAR_PRICE',
        'SOUTH_AFRICA_(ZAR)': 'ZAR_PRICE'
    }
    raw_df = raw_df.rename(columns=column_mapping)
    
    # Verify required columns
    required_cols = {'DATE', 'USD_PRICE'}
    missing = required_cols - set(raw_df.columns)
    if missing:
        raise KeyError(f"Missing columns in raw data: {missing}")
    
    # Clean dates
    raw_df['Clean_Date'] = raw_df['DATE'].apply(clean_date)
    valid_df = raw_df.dropna(subset=['Clean_Date'])
    
    # Process valid data
    valid_df['DATE'] = valid_df['Clean_Date']
    valid_df = valid_df.drop(columns=['Clean_Date'])
    
    # Date validation
    date_validation = (
        valid_df['DATE'].dt.year.between(1979, 2025) &
        valid_df['DATE'].dt.month.between(1, 12) &
        valid_df['DATE'].dt.day.between(1, 31)
    )
    valid_df = valid_df[date_validation]
    
    # Feature engineering for all currency columns
    currency_columns = [col for col in valid_df.columns if col.endswith('_PRICE')]
    for col in currency_columns:
        valid_df[f'MA_7_{col}'] = valid_df[col].rolling(7).mean()
        valid_df[f'MA_30_{col}'] = valid_df[col].rolling(30).mean()
        valid_df[f'Volatility_30_{col}'] = valid_df[col].pct_change().rolling(30).std()
        valid_df[f'Momentum_7_{col}'] = valid_df[col].diff(7)
    
    # Save processed data
    version = pd.Timestamp.now().strftime('%Y%m%d')
    output_path = Path(output_dir) / f'gold_processed_{version}.csv'
    valid_df.to_csv(output_path, index=False)
    
    return {
        'processed_path': str(output_path),
        'processed_count': len(valid_df)
    }

if __name__ == '__main__':
    result = preprocess_gold_data('data/1979-2021.csv')
    print(f"Processed {result['processed_count']} valid records")
    print(f"Clean data saved to: {result['processed_path']}")
