# Environment Variables for Gold Price Analyzer
# Copy this file to .env and fill in your actual API keys

# NewsData.io API Key for fetching gold-related news
# Get your free API key from: https://newsdata.io/
NEWSDATA_API_KEY=your_newsdata_api_key_here

# Flask Configuration
FLASK_ENV=development
FLASK_DEBUG=True

# Database Configuration (if you add MongoDB later)
# MONGODB_URI=your_mongodb_connection_string_here
# MONGODB_DB_NAME=gold_price_analyzer

# Other API Keys (add as needed)
# ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key_here
# QUANDL_API_KEY=your_quandl_key_here
