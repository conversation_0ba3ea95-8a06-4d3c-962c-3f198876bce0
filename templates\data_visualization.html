{% extends "new_base.html" %}

{% block content %}
<div class="visualization-container">
    <div class="header-section">
        <h1>Gold Price Analysis</h1>
        <div class="period-selector">
            <button class="period-btn active">Current period</button>
            <button class="period-btn">Quarter</button>
            <button class="period-btn">Year</button>
        </div>
    </div>

    <div class="chart-container">
        <div id="priceChart"></div>
    </div>

    <div class="controls-section">
        <div class="left-controls">
            <button class="filter-btn">
                <i class="fas fa-filter"></i> Filters
            </button>
            <select class="view-mode">
                <option>Currency view</option>
                <option>Region view</option>
            </select>
            <select class="grouping">
                <option>Not grouped</option>
                <option>By Region</option>
                <option>By Currency</option>
            </select>
            <select class="sorting">
                <option>Sort by Date</option>
                <option>Sort by Price</option>
            </select>
        </div>
        <div class="right-controls">
            <input type="text" placeholder="Search currency, region..." class="search-input">
            <button class="columns-btn">
                <i class="fas fa-columns"></i> Columns
            </button>
        </div>
    </div>

    <div class="data-table">
        <table>
            <thead>
                <tr>
                    <th>Month</th>
                    <th>USD Price</th>
                    <th>EUR Price</th>
                    <th>JPY Price</th>
                    <th>INR Price</th>
                </tr>
            </thead>
            <tbody>
                {% for row in table_data %}
                <tr>
                    <td>{{ row.month }}</td>
                    <td>{{ row.actual_usd }}</td>
                    <td>{{ row.actual_eur }}</td>
                    <td>{{ row.actual_jpy }}</td>
                    <td>{{ row.actual_inr }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>

<!-- Include Plotly.js -->
<script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
<!-- Include Font Awesome for icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Parse data from Flask
        const months = {{ months|tojson }};
        const actualValues = {{ actual_values|tojson }};
        const forecastValues = {{ forecast_values|tojson }};

        // Create traces for actual and forecast data
        const actualTrace = {
            x: months,
            y: actualValues,
            name: 'Actual Price',
            type: 'scatter',
            mode: 'lines+markers',
            line: {
                color: '#1f77b4',
                width: 2
            },
            marker: {
                size: 6
            }
        };

        const forecastTrace = {
            x: months,
            y: forecastValues,
            name: 'Forecast',
            type: 'scatter',
            mode: 'lines+markers',
            line: {
                color: '#ff7f0e',
                width: 2,
                dash: 'dash'
            },
            marker: {
                size: 6
            }
        };

        const layout = {
            title: 'Gold Price Trends and Forecast',
            xaxis: {
                title: 'Month',
                showgrid: true,
                gridcolor: '#E4E4E4'
            },
            yaxis: {
                title: 'Price (USD)',
                showgrid: true,
                gridcolor: '#E4E4E4'
            },
            plot_bgcolor: 'white',
            paper_bgcolor: 'white',
            showlegend: true,
            legend: {
                x: 0,
                y: 1
            },
            margin: {
                l: 50,
                r: 50,
                t: 50,
                b: 50
            }
        };

        Plotly.newPlot('priceChart', [actualTrace, forecastTrace], layout);
    });
</script>

<style>
.visualization-container {
    padding: 20px;
    max-width: 1400px;
    margin: 0 auto;
    background: #ffffff;
}

.header-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.period-selector {
    display: flex;
    gap: 10px;
}

.period-btn {
    padding: 8px 16px;
    border: 1px solid #ddd;
    background: white;
    border-radius: 4px;
    cursor: pointer;
}

.period-btn.active {
    background: #f0f0f0;
}

.chart-container {
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid #eee;
    border-radius: 8px;
    background: white;
}

.controls-section {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    gap: 20px;
}

.left-controls, .right-controls {
    display: flex;
    gap: 10px;
    align-items: center;
}

.filter-btn, .columns-btn {
    padding: 8px 16px;
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
}

select, .search-input {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    min-width: 150px;
}

.data-table {
    overflow-x: auto;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

th, td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

th {
    background: #f8f9fa;
    font-weight: 600;
}

tr:hover {
    background: #f8f9fa;
}

@media (max-width: 768px) {
    .controls-section {
        flex-direction: column;
    }
    
    .left-controls, .right-controls {
        width: 100%;
    }
}
</style>
{% endblock %}
