{% extends "new_base.html" %}

{% block content %}
<div class="market-analysis">
    <h1>Gold Market Analysis</h1>
    
    <div class="price-overview">
        <div class="price-card">
            <h2>Latest Gold Price (USD)</h2>
            <p class="price">${{ latest_price }}</p>
            <p class="subtitle">7-Day Average: ${{ week_avg }}</p>
        </div>
    </div>

    <div class="currency-grid">
        <h2>Global Market Overview</h2>
        <div class="currency-cards">
            {% for code, data in price_changes.items() %}
            <div class="currency-card">
                <h3>{{ code }}</h3>
                <p class="current-price">{{ data.current }}</p>
                <p class="price-change {% if data.change > 0 %}positive{% else %}negative{% endif %}">
                    {{ data.change }}%
                </p>
            </div>
            {% endfor %}
        </div>
    </div>

    <div class="news-section">
        <h2>Latest Gold Market News</h2>
        <div class="news-grid">
            {% for article in news_data %}
            <div class="news-card">
                <h3>{{ article.title }}</h3>
                <p class="news-description">{{ article.description }}</p>
                <a href="{{ article.link }}" target="_blank" class="read-more">Read More</a>
            </div>
            {% endfor %}
        </div>
    </div>
</div>

<style>
.market-analysis {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.price-overview {
    margin-bottom: 30px;
}

.price-card {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.price {
    font-size: 2.5em;
    color: #2c3e50;
    margin: 10px 0;
}

.subtitle {
    color: #7f8c8d;
}

.currency-grid {
    margin-bottom: 30px;
}

.currency-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.currency-card {
    background: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.current-price {
    font-size: 1.5em;
    margin: 10px 0;
}

.price-change {
    font-weight: bold;
}

.positive {
    color: #27ae60;
}

.negative {
    color: #c0392b;
}

.news-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.news-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.news-card h3 {
    margin-top: 0;
    color: #2c3e50;
}

.news-description {
    color: #7f8c8d;
    margin: 10px 0;
}

.read-more {
    display: inline-block;
    color: #3498db;
    text-decoration: none;
    margin-top: 10px;
}

.read-more:hover {
    text-decoration: underline;
}
</style>
{% endblock %}
