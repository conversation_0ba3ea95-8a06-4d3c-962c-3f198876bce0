from flask import Flask, render_template, request, jsonify
import pandas as pd
import requests
from data_preprocessor import preprocess_gold_data
from ml_model import validate_model, load_data, train_model, predict_future_prices
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

app = Flask(__name__, template_folder='templates')
app.config['TEMPLATES_AUTO_RELOAD'] = False
app.config['SEND_FILE_MAX_AGE_DEFAULT'] = 0

# Home Page
@app.route('/')
def home():
    return render_template('new_index.html')

# Market Analysis Page
@app.route('/market-analysis')
def market_analysis():
    try:
        print("Loading data...")
        data = load_data(file_type='prediction')
        print(f"Data loaded with shape: {data.shape}")
        print(f"Data columns: {data.columns}")
        
        latest_price = data['USD_PRICE'].iloc[-1]
        week_avg = data['USD_PRICE'].tail(7).mean()
        print(f"Latest price: {latest_price}, Weekly average: {week_avg}")

        # Fetch news data
        api_key = os.getenv('NEWSDATA_API_KEY')
        if not api_key:
            news_data = {"error": "API key not configured"}
            print("NewsData API key not found in environment variables")
        else:
            api_url = f"https://newsdata.io/api/1/news?apikey={api_key}&q=gold&country=in&language=en&category=business"
            response = requests.get(api_url)
            if response.status_code == 200:
                news_data = response.json()
                print(f"News data fetched successfully: {news_data}")
            else:
                news_data = {"error": "Failed to fetch news"}
                print("Failed to fetch news")

        return render_template(
            'market_analysis.html',
            latest_price=round(latest_price, 2),
            week_avg=round(week_avg, 2),
            news_data=news_data.get('results', [])
        )
    except Exception as e:
        print(f"Error in market_analysis: {str(e)}")
        return render_template('error.html', message=str(e)), 500

# Preprocess Data Route
@app.route('/preprocess-data')
def preprocess_data():
    try:
        result = preprocess_gold_data('data/1979-2021.csv')
        return jsonify({
            'message': f"Processed {result['processed_count']} valid records",
            'processed_path': result['processed_path']
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Price Prediction Page
@app.route('/price-prediction', methods=['GET', 'POST'])
def price_prediction():
    data = load_data('prediction')
    if request.method == 'POST':
        country = request.form.get('country')
        model, _ = train_model(data, country)
        future_prices = predict_future_prices(model, data[data['Country'] == country], future_days=30)
        return render_template('price_prediction.html', future_prices=future_prices, country=country)
    else:
        countries = data['Country'].unique()
        return render_template('price_prediction.html', countries=countries)

if __name__ == '__main__':
    app.run(debug=True)